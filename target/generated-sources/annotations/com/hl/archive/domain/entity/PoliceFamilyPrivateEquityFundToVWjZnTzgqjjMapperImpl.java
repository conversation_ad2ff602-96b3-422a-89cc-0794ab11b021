package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnTzgqjj;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-27T15:17:25+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyPrivateEquityFundToVWjZnTzgqjjMapperImpl implements PoliceFamilyPrivateEquityFundToVWjZnTzgqjjMapper {

    @Override
    public VWjZnTzgqjj convert(PoliceFamilyPrivateEquityFund source) {
        if ( source == null ) {
            return null;
        }

        VWjZnTzgqjj vWjZnTzgqjj = new VWjZnTzgqjj();

        if ( source.getTotalPaidAmount() != null ) {
            vWjZnTzgqjj.setZje( source.getTotalPaidAmount().toString() );
        }
        if ( source.getContractSigningDate() != null ) {
            vWjZnTzgqjj.setQsrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getContractSigningDate() ) );
        }
        vWjZnTzgqjj.setJjtx( source.getFundInvestmentDirection() );
        if ( source.getPersonalPaidAmount() != null ) {
            vWjZnTzgqjj.setGrje( source.getPersonalPaidAmount().toString() );
        }
        vWjZnTzgqjj.setGmsfhm( source.getIdCard() );
        if ( source.getSubscriptionAmount() != null ) {
            vWjZnTzgqjj.setRjje( source.getSubscriptionAmount().toString() );
        }
        vWjZnTzgqjj.setMc( source.getFundNameCode() );
        vWjZnTzgqjj.setXmFr( source.getName() );
        if ( source.getContractExpiryDate() != null ) {
            vWjZnTzgqjj.setJzrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getContractExpiryDate() ) );
        }

        return vWjZnTzgqjj;
    }

    @Override
    public VWjZnTzgqjj convert(PoliceFamilyPrivateEquityFund source, VWjZnTzgqjj target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getTotalPaidAmount() != null ) {
            target.setZje( source.getTotalPaidAmount().toString() );
        }
        else {
            target.setZje( null );
        }
        if ( source.getContractSigningDate() != null ) {
            target.setQsrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getContractSigningDate() ) );
        }
        else {
            target.setQsrq( null );
        }
        target.setJjtx( source.getFundInvestmentDirection() );
        if ( source.getPersonalPaidAmount() != null ) {
            target.setGrje( source.getPersonalPaidAmount().toString() );
        }
        else {
            target.setGrje( null );
        }
        target.setGmsfhm( source.getIdCard() );
        if ( source.getSubscriptionAmount() != null ) {
            target.setRjje( source.getSubscriptionAmount().toString() );
        }
        else {
            target.setRjje( null );
        }
        target.setMc( source.getFundNameCode() );
        target.setXmFr( source.getName() );
        if ( source.getContractExpiryDate() != null ) {
            target.setJzrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getContractExpiryDate() ) );
        }
        else {
            target.setJzrq( null );
        }

        return target;
    }
}
