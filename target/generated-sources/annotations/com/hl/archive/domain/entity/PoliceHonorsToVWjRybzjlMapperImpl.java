package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRybzjl;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-27T15:17:26+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceHonorsToVWjRybzjlMapperImpl implements PoliceHonorsToVWjRybzjlMapper {

    @Override
    public VWjRybzjl convert(PoliceHonors source) {
        if ( source == null ) {
            return null;
        }

        VWjRybzjl vWjRybzjl = new VWjRybzjl();

        if ( source.getAwardDate() != null ) {
            vWjRybzjl.setJcsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getAwardDate() ) );
        }
        vWjRybzjl.setBh( source.getBh() );
        vWjRybzjl.setGmsfhm( source.getIdCard() );
        vWjRybzjl.setJcpjjgmc( source.getApproveAuthority() );
        vWjRybzjl.setJcmc( source.getHonorName() );

        return vWjRybzjl;
    }

    @Override
    public VWjRybzjl convert(PoliceHonors source, VWjRybzjl target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getAwardDate() != null ) {
            target.setJcsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getAwardDate() ) );
        }
        else {
            target.setJcsj( null );
        }
        target.setBh( source.getBh() );
        target.setGmsfhm( source.getIdCard() );
        target.setJcpjjgmc( source.getApproveAuthority() );
        target.setJcmc( source.getHonorName() );

        return target;
    }
}
