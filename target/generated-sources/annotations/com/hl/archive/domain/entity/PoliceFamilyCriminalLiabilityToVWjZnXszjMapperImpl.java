package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnXszj;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-27T15:17:26+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyCriminalLiabilityToVWjZnXszjMapperImpl implements PoliceFamilyCriminalLiabilityToVWjZnXszjMapper {

    @Override
    public VWjZnXszj convert(PoliceFamilyCriminalLiability source) {
        if ( source == null ) {
            return null;
        }

        VWjZnXszj vWjZnXszj = new VWjZnXszj();

        vWjZnXszj.setXmDsr( source.getName() );
        vWjZnXszj.setGmsfhm( source.getIdCard() );
        if ( source.getProsecutionDate() != null ) {
            vWjZnXszj.setBzjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getProsecutionDate() ) );
        }
        vWjZnXszj.setCljg( source.getHandlingResult() );
        vWjZnXszj.setBzjyy( source.getProsecutionReason() );
        vWjZnXszj.setCljd( source.getHandlingStage() );

        return vWjZnXszj;
    }

    @Override
    public VWjZnXszj convert(PoliceFamilyCriminalLiability source, VWjZnXszj target) {
        if ( source == null ) {
            return target;
        }

        target.setXmDsr( source.getName() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getProsecutionDate() != null ) {
            target.setBzjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getProsecutionDate() ) );
        }
        else {
            target.setBzjsj( null );
        }
        target.setCljg( source.getHandlingResult() );
        target.setBzjyy( source.getProsecutionReason() );
        target.setCljd( source.getHandlingStage() );

        return target;
    }
}
