package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnTzgqjj;
import com.hl.orasync.domain.VWjZnTzgqjjToPoliceFamilyPrivateEquityFundMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__625;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__625.class,
    uses = {ConversionUtils.class,VWjZnTzgqjjToPoliceFamilyPrivateEquityFundMapper.class},
    imports = {}
)
public interface PoliceFamilyPrivateEquityFundToVWjZnTzgqjjMapper extends BaseMapper<PoliceFamilyPrivateEquityFund, VWjZnTzgqjj> {
  @Mapping(
      target = "zje",
      source = "totalPaidAmount"
  )
  @Mapping(
      target = "qsrq",
      source = "contractSigningDate"
  )
  @Mapping(
      target = "jjtx",
      source = "fundInvestmentDirection"
  )
  @Mapping(
      target = "grje",
      source = "personalPaidAmount"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "rjje",
      source = "subscriptionAmount"
  )
  @Mapping(
      target = "mc",
      source = "fundNameCode"
  )
  @Mapping(
      target = "xmFr",
      source = "name"
  )
  @Mapping(
      target = "jzrq",
      source = "contractExpiryDate"
  )
  VWjZnTzgqjj convert(PoliceFamilyPrivateEquityFund source);

  @Mapping(
      target = "zje",
      source = "totalPaidAmount"
  )
  @Mapping(
      target = "qsrq",
      source = "contractSigningDate"
  )
  @Mapping(
      target = "jjtx",
      source = "fundInvestmentDirection"
  )
  @Mapping(
      target = "grje",
      source = "personalPaidAmount"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "rjje",
      source = "subscriptionAmount"
  )
  @Mapping(
      target = "mc",
      source = "fundNameCode"
  )
  @Mapping(
      target = "xmFr",
      source = "name"
  )
  @Mapping(
      target = "jzrq",
      source = "contractExpiryDate"
  )
  VWjZnTzgqjj convert(PoliceFamilyPrivateEquityFund source, @MappingTarget VWjZnTzgqjj target);
}
