package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWsks;
import com.hl.orasync.domain.VWjWsksToPoliceOnlineExamMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__625;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__625.class,
    uses = {ConversionUtils.class,VWjWsksToPoliceOnlineExamMapper.class},
    imports = {}
)
public interface PoliceOnlineExamToVWjWsksMapper extends BaseMapper<PoliceOnlineExam, VWjWsks> {
  @Mapping(
      target = "jssj",
      source = "endTime"
  )
  @Mapping(
      target = "df",
      source = "score"
  )
  @Mapping(
      target = "tmgs",
      source = "questionCount"
  )
  @Mapping(
      target = "sfjj",
      source = "submitStatus"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "sjmc",
      source = "examPaperName"
  )
  @Mapping(
      target = "kssc",
      source = "examDuration"
  )
  @Mapping(
      target = "kssj",
      source = "startTime"
  )
  VWjWsks convert(PoliceOnlineExam source);

  @Mapping(
      target = "jssj",
      source = "endTime"
  )
  @Mapping(
      target = "df",
      source = "score"
  )
  @Mapping(
      target = "tmgs",
      source = "questionCount"
  )
  @Mapping(
      target = "sfjj",
      source = "submitStatus"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "sjmc",
      source = "examPaperName"
  )
  @Mapping(
      target = "kssc",
      source = "examDuration"
  )
  @Mapping(
      target = "kssj",
      source = "startTime"
  )
  VWjWsks convert(PoliceOnlineExam source, @MappingTarget VWjWsks target);
}
