package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilySecuritiesInsurance;
import com.hl.archive.domain.entity.PoliceFamilySecuritiesInsuranceToVWjQtGpjjtzMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__625;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__625.class,
    uses = {ConversionUtils.class,PoliceFamilySecuritiesInsuranceToVWjQtGpjjtzMapper.class},
    imports = {}
)
public interface VWjQtGpjjtzToPoliceFamilySecuritiesInsuranceMapper extends BaseMapper<VWjQtGpjjtz, PoliceFamilySecuritiesInsurance> {
  @Mapping(
      target = "netValuePremium",
      source = "rjz"
  )
  @Mapping(
      target = "holderName",
      source = "xmCyr"
  )
  @Mapping(
      target = "securityNameCode",
      source = "mcdm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "holdingQuantity",
      source = "sl"
  )
  PoliceFamilySecuritiesInsurance convert(VWjQtGpjjtz source);

  @Mapping(
      target = "netValuePremium",
      source = "rjz"
  )
  @Mapping(
      target = "holderName",
      source = "xmCyr"
  )
  @Mapping(
      target = "securityNameCode",
      source = "mcdm"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "holdingQuantity",
      source = "sl"
  )
  PoliceFamilySecuritiesInsurance convert(VWjQtGpjjtz source,
      @MappingTarget PoliceFamilySecuritiesInsurance target);
}
