package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceInjuryDeclare;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-27T15:17:26+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjMjqyxxsbToPoliceInjuryDeclareMapperImpl implements VWjMjqyxxsbToPoliceInjuryDeclareMapper {

    @Override
    public PoliceInjuryDeclare convert(VWjMjqyxxsb source) {
        if ( source == null ) {
            return null;
        }

        PoliceInjuryDeclare policeInjuryDeclare = new PoliceInjuryDeclare();

        policeInjuryDeclare.setPoliceNumber( source.getJh() );
        policeInjuryDeclare.setOrgName( source.getGzdwGajgmc() );
        policeInjuryDeclare.setCurrentStatus( source.getHcrdztmc() );
        policeInjuryDeclare.setDeclareType( source.getSqlbmc() );
        policeInjuryDeclare.setIdCard( source.getGmsfhm() );
        policeInjuryDeclare.setName( source.getXm() );
        policeInjuryDeclare.setPosition( source.getZwmc() );
        policeInjuryDeclare.setInjuryEvent( source.getSqly() );

        return policeInjuryDeclare;
    }

    @Override
    public PoliceInjuryDeclare convert(VWjMjqyxxsb source, PoliceInjuryDeclare target) {
        if ( source == null ) {
            return target;
        }

        target.setPoliceNumber( source.getJh() );
        target.setOrgName( source.getGzdwGajgmc() );
        target.setCurrentStatus( source.getHcrdztmc() );
        target.setDeclareType( source.getSqlbmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setName( source.getXm() );
        target.setPosition( source.getZwmc() );
        target.setInjuryEvent( source.getSqly() );

        return target;
    }
}
