package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceDrinkReport;
import com.hl.archive.domain.entity.PoliceDrinkReportToVWjYjbbMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__625;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__625.class,
    uses = {ConversionUtils.class,PoliceDrinkReportToVWjYjbbMapper.class},
    imports = {}
)
public interface VWjYjbbToPoliceDrinkReportMapper extends BaseMapper<VWjYjbb, PoliceDrinkReport> {
  @Mapping(
      target = "organizationId",
      source = "dwbm"
  )
  @Mapping(
      target = "reason",
      source = "yjsy"
  )
  @Mapping(
      target = "approveResult",
      source = "shjg"
  )
  @Mapping(
      target = "drinkTime",
      source = "yjrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "xxzj",
      source = "xxzjbh"
  )
  @Mapping(
      target = "remark",
      source = "bz"
  )
  @Mapping(
      target = "inviter",
      source = "yyrxm"
  )
  @Mapping(
      target = "location",
      source = "yjdd"
  )
  @Mapping(
      target = "payer",
      source = "fkrxm"
  )
  @Mapping(
      target = "travelMode",
      source = "cxfs"
  )
  @Mapping(
      target = "participants",
      source = "cyrs"
  )
  PoliceDrinkReport convert(VWjYjbb source);

  @Mapping(
      target = "organizationId",
      source = "dwbm"
  )
  @Mapping(
      target = "reason",
      source = "yjsy"
  )
  @Mapping(
      target = "approveResult",
      source = "shjg"
  )
  @Mapping(
      target = "drinkTime",
      source = "yjrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "xxzj",
      source = "xxzjbh"
  )
  @Mapping(
      target = "remark",
      source = "bz"
  )
  @Mapping(
      target = "inviter",
      source = "yyrxm"
  )
  @Mapping(
      target = "location",
      source = "yjdd"
  )
  @Mapping(
      target = "payer",
      source = "fkrxm"
  )
  @Mapping(
      target = "travelMode",
      source = "cxfs"
  )
  @Mapping(
      target = "participants",
      source = "cyrs"
  )
  PoliceDrinkReport convert(VWjYjbb source, @MappingTarget PoliceDrinkReport target);
}
