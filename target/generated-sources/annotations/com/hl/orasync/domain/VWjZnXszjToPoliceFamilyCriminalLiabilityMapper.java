package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyCriminalLiability;
import com.hl.archive.domain.entity.PoliceFamilyCriminalLiabilityToVWjZnXszjMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__625;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__625.class,
    uses = {ConversionUtils.class,PoliceFamilyCriminalLiabilityToVWjZnXszjMapper.class},
    imports = {}
)
public interface VWjZnXszjToPoliceFamilyCriminalLiabilityMapper extends BaseMapper<VWjZnXszj, PoliceFamilyCriminalLiability> {
  @Mapping(
      target = "prosecutionDate",
      source = "bzjsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "handlingStage",
      source = "cljd"
  )
  @Mapping(
      target = "name",
      source = "xmDsr"
  )
  @Mapping(
      target = "prosecutionReason",
      source = "bzjyy"
  )
  @Mapping(
      target = "handlingResult",
      source = "cljg"
  )
  PoliceFamilyCriminalLiability convert(VWjZnXszj source);

  @Mapping(
      target = "prosecutionDate",
      source = "bzjsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "handlingStage",
      source = "cljd"
  )
  @Mapping(
      target = "name",
      source = "xmDsr"
  )
  @Mapping(
      target = "prosecutionReason",
      source = "bzjyy"
  )
  @Mapping(
      target = "handlingResult",
      source = "cljg"
  )
  PoliceFamilyCriminalLiability convert(VWjZnXszj source,
      @MappingTarget PoliceFamilyCriminalLiability target);
}
