package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHkMacauTaiwanTravel;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-27T15:17:25+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrGatwlqkToPoliceHkMacauTaiwanTravelMapperImpl implements VWjBrGatwlqkToPoliceHkMacauTaiwanTravelMapper {

    @Override
    public PoliceHkMacauTaiwanTravel convert(VWjBrGatwlqk source) {
        if ( source == null ) {
            return null;
        }

        PoliceHkMacauTaiwanTravel policeHkMacauTaiwanTravel = new PoliceHkMacauTaiwanTravel();

        policeHkMacauTaiwanTravel.setDestinationRegion( source.getSdgj() );
        policeHkMacauTaiwanTravel.setEndDate( ConversionUtils.strToDate( source.getJssj() ) );
        policeHkMacauTaiwanTravel.setDocumentNumber( source.getZjhm() );
        policeHkMacauTaiwanTravel.setIdCard( source.getGmsfhm() );
        policeHkMacauTaiwanTravel.setTravelReason( source.getSy() );
        policeHkMacauTaiwanTravel.setApprovalAuthority( source.getSpjgmc() );
        policeHkMacauTaiwanTravel.setStartDate( ConversionUtils.strToDate( source.getKssj() ) );

        return policeHkMacauTaiwanTravel;
    }

    @Override
    public PoliceHkMacauTaiwanTravel convert(VWjBrGatwlqk source, PoliceHkMacauTaiwanTravel target) {
        if ( source == null ) {
            return target;
        }

        target.setDestinationRegion( source.getSdgj() );
        target.setEndDate( ConversionUtils.strToDate( source.getJssj() ) );
        target.setDocumentNumber( source.getZjhm() );
        target.setIdCard( source.getGmsfhm() );
        target.setTravelReason( source.getSy() );
        target.setApprovalAuthority( source.getSpjgmc() );
        target.setStartDate( ConversionUtils.strToDate( source.getKssj() ) );

        return target;
    }
}
