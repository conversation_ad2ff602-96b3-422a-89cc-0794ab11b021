package com.hl.orasync.sync;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.orasync.annotation.ExtensionField;
import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 扩展字段保留功能测试
 */
public class ExtensionFieldTest {

    @Mock
    private ExtensionFieldConfig extensionFieldConfig;

    private DataSyncService dataSyncService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        dataSyncService = new DataSyncService(extensionFieldConfig);
    }

    @Test
    void testMergeRecordsWithExtensionFields() throws Exception {
        // 准备测试数据
        TestEntity sourceRecord = new TestEntity();
        sourceRecord.setId(1L);
        sourceRecord.setName("Updated Name");
        sourceRecord.setDescription("Updated Description");
        sourceRecord.setCustomField("Source Custom");
        sourceRecord.setUserRemark("Source Remark");

        TestEntity targetRecord = new TestEntity();
        targetRecord.setId(1L);
        targetRecord.setName("Original Name");
        targetRecord.setDescription("Original Description");
        targetRecord.setCustomField("Target Custom");
        targetRecord.setUserRemark("Target Remark");

        // 配置扩展字段
        when(extensionFieldConfig.isExtensionField(TestEntity.class, "customField"))
                .thenReturn(true);

        // 使用反射调用私有方法
        Method mergeMethod = DataSyncService.class.getDeclaredMethod("mergeRecords", Object.class, Object.class);
        mergeMethod.setAccessible(true);
        
        TestEntity result = (TestEntity) mergeMethod.invoke(dataSyncService, sourceRecord, targetRecord);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId()); // 主键保持不变
        assertEquals("Updated Name", result.getName()); // 普通字段被更新
        assertEquals("Updated Description", result.getDescription()); // 普通字段被更新
        assertEquals("Target Custom", result.getCustomField()); // 扩展字段保持原值
        assertEquals("Target Remark", result.getUserRemark()); // 注解标记的扩展字段保持原值
    }

    @Test
    void testCompareAllDataWithExtensionFields() throws Exception {
        // 准备源数据
        Map<String, TestEntity> sourceDataMap = new HashMap<>();
        TestEntity sourceEntity = new TestEntity();
        sourceEntity.setId(1L);
        sourceEntity.setName("Updated Name");
        sourceEntity.setCustomField("Source Custom");
        sourceDataMap.put("1", sourceEntity);

        // 准备目标数据
        Map<String, TestEntity> targetDataMap = new HashMap<>();
        TestEntity targetEntity = new TestEntity();
        targetEntity.setId(1L);
        targetEntity.setName("Original Name");
        targetEntity.setCustomField("Target Custom");
        targetDataMap.put("1", targetEntity);

        // 配置扩展字段
        when(extensionFieldConfig.isExtensionField(TestEntity.class, "customField"))
                .thenReturn(true);

        // 业务键生成器
        Function<TestEntity, String> keyGenerator = entity -> String.valueOf(entity.getId());

        // 使用反射调用私有方法
        Method compareMethod = DataSyncService.class.getDeclaredMethod("compareAllData", 
                Map.class, Map.class, Function.class);
        compareMethod.setAccessible(true);
        
        DataCompareResult<TestEntity> result = (DataCompareResult<TestEntity>) 
                compareMethod.invoke(dataSyncService, sourceDataMap, targetDataMap, keyGenerator);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getToInsert().size()); // 没有新增
        assertEquals(1, result.getToUpdate().size()); // 有一个更新
        assertEquals(0, result.getToDelete().size()); // 没有删除

        TestEntity updatedEntity = result.getToUpdate().get(0);
        assertEquals("Updated Name", updatedEntity.getName()); // 普通字段被更新
        assertEquals("Target Custom", updatedEntity.getCustomField()); // 扩展字段保持原值
    }

    /**
     * 测试实体类
     */
    @Data
    @TableName("test_entity")
    static class TestEntity {
        @TableId
        private Long id;

        @TableField("name")
        private String name;

        @TableField("description")
        private String description;

        @TableField("custom_field")
        private String customField;

        @ExtensionField("用户备注")
        @TableField("user_remark")
        private String userRemark;

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestEntity that = (TestEntity) obj;
            return java.util.Objects.equals(name, that.name) &&
                   java.util.Objects.equals(description, that.description);
        }

        @Override
        public int hashCode() {
            return java.util.Objects.hash(name, description);
        }
    }
}
