package com.hl.orasync.sync;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 扩展字段配置类
 * 用于配置哪些字段需要在数据同步时保留
 */
@Component
@ConfigurationProperties(prefix = "data-sync.extension-fields")
public class ExtensionFieldConfig {

    /**
     * 全局扩展字段前缀
     * 以这些前缀开头的字段将被视为扩展字段
     */
    private List<String> globalPrefixes = ListUtil.of("ext_", "custom_", "user_");

    /**
     * 全局扩展字段名称
     * 这些字段名将被视为扩展字段
     */
    private List<String> globalFieldNames = ListUtil.of("remark", "tags", "customField1", "customField2", "extendInfo");

    /**
     * 按实体类配置的扩展字段
     * key: 实体类名称
     * value: 该实体类的扩展字段列表
     */
    private Map<String, List<String>> entitySpecificFields = new HashMap<>();

    /**
     * 检查字段是否为扩展字段
     * @param entityClass 实体类
     * @param fieldName 字段名
     * @return true if 是扩展字段
     */
    public boolean isExtensionField(Class<?> entityClass, String fieldName) {
        // 检查全局前缀
        for (String prefix : globalPrefixes) {
            if (fieldName.startsWith(prefix)) {
                return true;
            }
        }

        // 检查全局字段名
        if (globalFieldNames.contains(fieldName)) {
            return true;
        }

        // 检查实体类特定字段
        String className = entityClass.getSimpleName();
        List<String> specificFields = entitySpecificFields.get(className);
        if (specificFields != null && specificFields.contains(fieldName)) {
            return true;
        }

        return false;
    }

    /**
     * 添加实体类特定的扩展字段
     * @param entityClass 实体类
     * @param fieldNames 字段名列表
     */
    public void addEntitySpecificFields(Class<?> entityClass, List<String> fieldNames) {
        entitySpecificFields.put(entityClass.getSimpleName(), fieldNames);
    }

    // Getters and Setters
    public List<String> getGlobalPrefixes() {
        return globalPrefixes;
    }

    public void setGlobalPrefixes(List<String> globalPrefixes) {
        this.globalPrefixes = globalPrefixes;
    }

    public List<String> getGlobalFieldNames() {
        return globalFieldNames;
    }

    public void setGlobalFieldNames(List<String> globalFieldNames) {
        this.globalFieldNames = globalFieldNames;
    }

    public Map<String, List<String>> getEntitySpecificFields() {
        return entitySpecificFields;
    }

    public void setEntitySpecificFields(Map<String, List<String>> entitySpecificFields) {
        this.entitySpecificFields = entitySpecificFields;
    }
}
