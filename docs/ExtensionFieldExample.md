# 扩展字段实际使用示例

## 场景描述

假设您在 `PoliceBasicInfo` 表中添加了以下扩展字段：
- `custom_tags`: 用户自定义标签
- `admin_remark`: 管理员备注
- `audit_status`: 审核状态
- `last_modified_by`: 最后修改人

## 实现步骤

### 1. 修改实体类

```java
@Data
@TableName("police_basic_info")
public class PoliceBasicInfo {
    @TableId
    private Long id;
    
    // 原有字段
    private String name;
    private String idCard;
    private String unitName;
    // ... 其他原有字段
    
    // 新增扩展字段
    @ExtensionField("用户自定义标签")
    @TableField("custom_tags")
    private String customTags;
    
    @ExtensionField("管理员备注")
    @TableField("admin_remark")
    private String adminRemark;
    
    @ExtensionField("审核状态")
    @TableField("audit_status")
    private String auditStatus;
    
    @ExtensionField("最后修改人")
    @TableField("last_modified_by")
    private String lastModifiedBy;
}
```

### 2. 数据库表结构

```sql
ALTER TABLE police_basic_info 
ADD COLUMN custom_tags VARCHAR(500) COMMENT '用户自定义标签',
ADD COLUMN admin_remark TEXT COMMENT '管理员备注',
ADD COLUMN audit_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审核状态',
ADD COLUMN last_modified_by VARCHAR(50) COMMENT '最后修改人';
```

### 3. 配置扩展字段

在 `application.yml` 中添加配置：

```yaml
data-sync:
  extension-fields:
    entity-specific-fields:
      PoliceBasicInfo:
        - "customTags"
        - "adminRemark"
        - "auditStatus"
        - "lastModifiedBy"
```

### 4. 验证同步效果

#### 同步前的数据状态

**源数据库 (Oracle)**:
```
ID | NAME | ID_CARD | UNIT_NAME
1  | 张三 | 123456  | 派出所A
```

**目标数据库 (MySQL)**:
```
ID | NAME | ID_CARD | UNIT_NAME | CUSTOM_TAGS | ADMIN_REMARK | AUDIT_STATUS
1  | 张三 | 123456  | 派出所B   | "重点关注"   | "表现优秀"    | "APPROVED"
```

#### 同步后的数据状态

**目标数据库 (MySQL)**:
```
ID | NAME | ID_CARD | UNIT_NAME | CUSTOM_TAGS | ADMIN_REMARK | AUDIT_STATUS
1  | 张三 | 123456  | 派出所A   | "重点关注"   | "表现优秀"    | "APPROVED"
```

**结果分析**:
- `UNIT_NAME` 从源数据库更新为 "派出所A"
- `CUSTOM_TAGS`、`ADMIN_REMARK`、`AUDIT_STATUS` 保持原值不变

## 高级配置

### 1. 条件性扩展字段

```java
@ExtensionField("关键扩展字段")
@TableField("critical_field")
private String criticalField;
```

### 2. 全局扩展字段规则

```yaml
data-sync:
  extension-fields:
    global-prefixes:
      - "user_"
      - "admin_"
      - "custom_"
    global-field-names:
      - "tags"
      - "remark"
      - "status"
```

### 3. 动态扩展字段配置

```java
@Component
public class ExtensionFieldConfigurer {
    
    @Autowired
    private ExtensionFieldConfig config;
    
    @PostConstruct
    public void configureExtensionFields() {
        // 动态添加扩展字段
        config.addEntitySpecificFields(PoliceBasicInfo.class, 
            Arrays.asList("dynamicField1", "dynamicField2"));
    }
}
```

## 监控和日志

### 1. 启用详细日志

```yaml
logging:
  level:
    com.hl.orasync.sync.DataSyncService: DEBUG
```

### 2. 监控扩展字段保留情况

```java
@Component
public class ExtensionFieldMonitor {
    
    @EventListener
    public void onSyncComplete(SyncCompleteEvent event) {
        log.info("同步完成，扩展字段保留统计: {}", event.getExtensionFieldStats());
    }
}
```

## 故障排除

### 常见问题及解决方案

1. **扩展字段仍被覆盖**
   ```
   原因：字段未正确配置为扩展字段
   解决：检查注解或配置文件
   ```

2. **同步性能下降**
   ```
   原因：反射操作过多
   解决：优化扩展字段配置，减少不必要的字段检查
   ```

3. **数据类型不匹配**
   ```
   原因：扩展字段类型与数据库不匹配
   解决：确保Java字段类型与数据库字段类型一致
   ```

## 最佳实践

1. **命名规范**：使用统一的扩展字段命名前缀
2. **文档记录**：详细记录每个扩展字段的用途
3. **版本控制**：扩展字段变更要有版本记录
4. **测试验证**：每次添加扩展字段后进行同步测试
5. **性能监控**：定期监控同步性能，优化配置
