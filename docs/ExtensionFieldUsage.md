# 数据同步扩展字段使用指南

## 概述

在数据同步过程中，目标表可能包含一些不存在于源表的扩展字段。为了在同步时保留这些扩展字段的值，我们提供了多种配置方式。

## 配置方式

### 1. 使用注解标记扩展字段

在实体类的字段上添加 `@ExtensionField` 注解：

```java
@Data
@TableName("police_basic_info")
public class PoliceBasicInfo {
    @TableId
    private Long id;
    
    private String name;
    private String idCard;
    
    // 扩展字段 - 使用注解标记
    @ExtensionField("用户自定义备注")
    @TableField("custom_remark")
    private String customRemark;
    
    @ExtensionField(value = "内部标签", critical = true)
    @TableField("internal_tags")
    private String internalTags;
}
```

### 2. 使用配置文件

在 `application.yml` 中配置扩展字段：

```yaml
data-sync:
  extension-fields:
    # 全局扩展字段前缀
    global-prefixes:
      - "ext_"
      - "custom_"
      - "user_"
    
    # 全局扩展字段名称
    global-field-names:
      - "remark"
      - "tags"
      - "customField1"
    
    # 按实体类配置的扩展字段
    entity-specific-fields:
      PoliceBasicInfo:
        - "customRemark"
        - "internalNotes"
```

### 3. 字段命名规则

按照命名规则自动识别扩展字段：

- 以 `ext_`、`custom_`、`user_` 开头的字段
- 名为 `remark`、`tags`、`customField1` 等的字段

## 工作原理

### 数据合并流程

1. **数据比对**：比较源数据和目标数据是否相同
2. **字段合并**：如果数据不同，将源数据的字段值更新到目标记录中
3. **扩展字段保留**：跳过扩展字段的更新，保留目标记录中的原值
4. **批量更新**：使用合并后的记录进行批量更新

### 核心方法

```java
private <T> T mergeRecords(T sourceRecord, T targetRecord) {
    // 获取所有字段
    Field[] fields = sourceRecord.getClass().getDeclaredFields();
    
    for (Field field : fields) {
        field.setAccessible(true);
        
        // 跳过扩展字段
        if (isExtensionField(field)) {
            continue;
        }
        
        // 更新非扩展字段
        Object sourceValue = field.get(sourceRecord);
        field.set(targetRecord, sourceValue);
    }
    
    return targetRecord;
}
```

## 使用示例

### 示例1：民警基本信息扩展

```java
@Data
@TableName("police_basic_info")
public class PoliceBasicInfo {
    @TableId
    private Long id;
    
    // 源数据字段
    private String name;
    private String idCard;
    private String unitName;
    
    // 扩展字段 - 不会被同步覆盖
    @ExtensionField("用户自定义标签")
    private String userTags;
    
    @ExtensionField("管理员备注")
    private String adminRemark;
    
    @ExtensionField("最后修改人")
    private String lastModifiedBy;
}
```

### 示例2：配置特定实体的扩展字段

```yaml
data-sync:
  extension-fields:
    entity-specific-fields:
      PoliceBasicInfo:
        - "userTags"
        - "adminRemark"
        - "lastModifiedBy"
        - "auditStatus"
      
      PoliceEducation:
        - "verificationNotes"
        - "additionalCertificates"
```

## 注意事项

1. **主键字段**：主键字段会自动被识别为扩展字段，不会被覆盖
2. **虚拟字段**：标记为 `@TableField(exist = false)` 的字段会被跳过
3. **性能考虑**：扩展字段的判断使用反射，建议合理配置以避免性能问题
4. **数据一致性**：确保扩展字段的数据类型和数据库字段类型匹配

## 故障排除

### 常见问题

1. **扩展字段被覆盖**
   - 检查字段是否正确配置为扩展字段
   - 确认注解是否正确添加
   - 查看日志中的字段合并信息

2. **同步失败**
   - 检查扩展字段的数据类型
   - 确认数据库字段是否存在
   - 查看异常日志

### 调试方法

启用调试日志：

```yaml
logging:
  level:
    com.hl.orasync.sync.DataSyncService: DEBUG
```

查看字段合并过程的详细日志。
